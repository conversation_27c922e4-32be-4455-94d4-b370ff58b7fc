import { supabase } from '@/lib/supabase'
import type { 
  HeadlineAnalysis, 
  CreateHeadlineAnalysisData, 
  UpdateHeadlineAnalysisData 
} from '@/lib/supabase'

/**
 * Service for managing headline analysis operations with Supabase
 * Following the exact same patterns as designAnalysisService.ts
 */
class HeadlineAnalysisService {
  
  /**
   * Save a new headline analysis to the database
   */
  async saveAnalysis(analysisData: CreateHeadlineAnalysisData): Promise<HeadlineAnalysis> {
    try {
      console.log('💾 Saving headline analysis to database:', {
        headline: analysisData.headline_text.substring(0, 50) + '...',
        userId: analysisData.user_id,
        score: analysisData.overall_score
      })

      // Check authentication first
      const { data: { user }, error: authError } = await supabase.auth.getUser()
      if (authError || !user) {
        console.error('❌ Authentication error in saveAnalysis:', authError)
        throw new Error('User not authenticated')
      }

      if (user.id !== analysisData.user_id) {
        console.error('❌ User ID mismatch in saveAnalysis:', { 
          requestedUserId: analysisData.user_id, 
          actualUserId: user.id 
        })
        throw new Error('User ID mismatch')
      }

      // Add timestamp
      const dataWithTimestamp = {
        ...analysisData,
        updated_at: new Date().toISOString()
      }

      const { data, error } = await supabase
        .from('headline_analyses')
        .insert(dataWithTimestamp)
        .select()
        .single()

      if (error) {
        console.error('❌ Error saving headline analysis:', error)

        // Handle specific database errors
        if (error.code === 'PGRST106') {
          throw new Error('Database schema configuration error. Please contact support.')
        } else if (error.message?.includes('permission denied') || error.message?.includes('RLS')) {
          throw new Error('Access denied. Please check your authentication.')
        } else {
          throw new Error(`Failed to save analysis: ${error.message}`)
        }
      }

      console.log('✅ Headline analysis saved successfully:', data.id)
      return data

    } catch (error) {
      console.error('💥 Exception in saveAnalysis:', error)
      throw error
    }
  }

  /**
   * Get all headline analyses for the current user
   */
  async getUserAnalyses(userId: string, options?: {
    limit?: number
    offset?: number
    toolType?: string
    isFavorite?: boolean
    orderBy?: 'created_at' | 'updated_at' | 'overall_score'
    orderDirection?: 'asc' | 'desc'
  }): Promise<HeadlineAnalysis[]> {
    try {
      console.log('🔍 getUserAnalyses called with:', { userId, options })

      // Validate user ID
      if (!userId || userId === 'anonymous') {
        console.warn('⚠️ Invalid user ID provided to getUserAnalyses')
        return []
      }

      // Check authentication first
      const { data: { user }, error: authError } = await supabase.auth.getUser()
      if (authError || !user) {
        console.error('❌ Authentication error in getUserAnalyses:', authError)
        throw new Error('User not authenticated')
      }

      if (user.id !== userId) {
        console.error('❌ User ID mismatch in getUserAnalyses:', { 
          requestedUserId: userId, 
          actualUserId: user.id 
        })
        throw new Error('User ID mismatch')
      }

      // Build query with explicit column selection
      let query = supabase
        .from('headline_analyses')
        .select(`
          id,
          created_at,
          updated_at,
          user_id,
          headline_text,
          content_type,
          audience_context,
          content_type_context,
          tool_type,
          analysis_version,
          overall_score,
          basic_analysis,
          advanced_analysis,
          recommendations,
          ai_analysis_summary,
          gemini_analysis,
          agent_message,
          analysis_duration_ms,
          processing_time,
          status,
          error_message,
          is_favorite,
          custom_name,
          tags,
          notes,
          view_count,
          last_viewed_at,
          regeneration_count
        `)
        .eq('user_id', userId)

      // Apply filters
      if (options?.toolType) {
        query = query.eq('tool_type', options.toolType)
      }

      if (options?.isFavorite !== undefined) {
        query = query.eq('is_favorite', options.isFavorite)
      }

      // Apply ordering
      const orderBy = options?.orderBy || 'created_at'
      const orderDirection = options?.orderDirection || 'desc'
      query = query.order(orderBy, { ascending: orderDirection === 'asc' })

      // Apply pagination
      if (options?.limit) {
        query = query.limit(options.limit)
      }

      if (options?.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 50) - 1)
      }

      console.log('📊 Executing query for headline analyses...')
      const { data, error } = await query

      if (error) {
        console.error('❌ Error fetching headline analyses:', error)
        
        if (error.message?.includes('permission denied') || error.message?.includes('RLS')) {
          throw new Error('Access denied. Please check your authentication.')
        } else if (error.code === 'PGRST106') {
          throw new Error('Database schema configuration error. Please contact support.')
        } else {
          throw new Error(`Failed to fetch analyses: ${error.message}`)
        }
      }

      console.log('✅ Successfully fetched headline analyses:', {
        count: data?.length || 0,
        userId,
        filters: options
      })

      return data || []

    } catch (error) {
      console.error('💥 Exception in getUserAnalyses:', error)
      throw error
    }
  }

  /**
   * Update a headline analysis
   */
  async updateAnalysis(updateData: UpdateHeadlineAnalysisData): Promise<HeadlineAnalysis> {
    const { id, ...updates } = updateData

    // Add timestamp
    const updatesWithTimestamp = {
      ...updates,
      updated_at: new Date().toISOString()
    }

    const { data, error } = await supabase
      .from('headline_analyses')
      .update(updatesWithTimestamp)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating headline analysis:', error)
      throw new Error(`Failed to update analysis: ${error.message}`)
    }

    return data
  }

  /**
   * Delete a headline analysis
   */
  async deleteAnalysis(id: string): Promise<void> {
    const { error } = await supabase
      .from('headline_analyses')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error deleting headline analysis:', error)
      throw new Error(`Failed to delete analysis: ${error.message}`)
    }
  }

  /**
   * Toggle favorite status of an analysis
   */
  async toggleFavorite(id: string): Promise<HeadlineAnalysis> {
    // First get the current analysis
    const { data: currentAnalysis, error: fetchError } = await supabase
      .from('headline_analyses')
      .select('is_favorite')
      .eq('id', id)
      .single()

    if (fetchError) {
      console.error('Error fetching current analysis:', fetchError)
      throw new Error(`Failed to fetch analysis: ${fetchError.message}`)
    }

    // Toggle the favorite status
    const newFavoriteStatus = !currentAnalysis.is_favorite

    return this.updateAnalysis({
      id,
      is_favorite: newFavoriteStatus
    })
  }

  /**
   * Get user statistics for headline analyses
   */
  async getUserStats(userId: string): Promise<{
    totalAnalyses: number
    favoriteAnalyses: number
    averageScore: number
    toolTypeBreakdown: Record<string, number>
    recentActivity: number
  }> {
    try {
      console.log('📊 Getting user stats for headline analyses:', userId)

      const analyses = await this.getUserAnalyses(userId, {
        limit: 1000,
        orderBy: 'created_at',
        orderDirection: 'desc'
      })

      // Calculate statistics
      const totalAnalyses = analyses.length
      const favoriteAnalyses = analyses.filter(a => a.is_favorite).length
      const averageScore = totalAnalyses > 0
        ? analyses.reduce((sum, a) => sum + a.overall_score, 0) / totalAnalyses
        : 0

      // Tool type breakdown
      const toolTypeBreakdown = analyses.reduce((acc, analysis) => {
        const toolType = analysis.tool_type || 'headline_analyzer'
        acc[toolType] = (acc[toolType] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      // Recent activity (last 7 days)
      const sevenDaysAgo = new Date()
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
      const recentActivity = analyses.filter(a =>
        new Date(a.created_at) > sevenDaysAgo
      ).length

      console.log('✅ getUserStats successful:', {
        totalAnalyses,
        favoriteAnalyses,
        averageScore: Math.round(averageScore * 100) / 100,
        toolTypeBreakdown,
        recentActivity
      })

      return {
        totalAnalyses,
        favoriteAnalyses,
        averageScore: Math.round(averageScore * 100) / 100,
        toolTypeBreakdown,
        recentActivity
      }
    } catch (error) {
      console.error('💥 Exception in getUserStats:', error)
      throw error
    }
  }

  /**
   * Get recent analyses (for history tab)
   * Returns the last 10 analyses, excluding favorites
   */
  async getRecentAnalyses(userId: string): Promise<HeadlineAnalysis[]> {
    return this.getUserAnalyses(userId, {
      limit: 10,
      orderBy: 'created_at',
      orderDirection: 'desc'
    })
  }

  /**
   * Get favorite analyses (for favorites tab)
   */
  async getFavoriteAnalyses(userId: string): Promise<HeadlineAnalysis[]> {
    return this.getUserAnalyses(userId, {
      isFavorite: true,
      orderBy: 'created_at',
      orderDirection: 'desc'
    })
  }

  /**
   * Update tags for an analysis
   */
  async updateTags(id: string, tags: string[]): Promise<HeadlineAnalysis> {
    const { data, error } = await supabase
      .from('headline_analyses')
      .update({ tags })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating tags:', error)
      throw new Error(`Failed to update tags: ${error.message}`)
    }

    return data
  }

  /**
   * Update notes for an analysis
   */
  async updateNotes(id: string, notes: string): Promise<HeadlineAnalysis> {
    const { data, error } = await supabase
      .from('headline_analyses')
      .update({ notes })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating notes:', error)
      throw new Error(`Failed to update notes: ${error.message}`)
    }

    return data
  }

  /**
   * Increment view count and update last viewed timestamp
   */
  async incrementViewCount(id: string): Promise<void> {
    const { error } = await supabase.rpc('increment_headline_view_count', {
      analysis_id: id
    })

    if (error) {
      console.error('Error incrementing view count:', error)
      // Don't throw error for view count updates as they're not critical
    }
  }

  /**
   * Clean up old analyses (keep only last 10 non-favorite analyses)
   */
  async cleanupOldAnalyses(userId: string): Promise<void> {
    try {
      // Get all non-favorite analyses ordered by creation date (newest first)
      const nonFavoriteAnalyses = await this.getUserAnalyses(userId, {
        isFavorite: false,
        orderBy: 'created_at',
        orderDirection: 'desc'
      })

      // If we have more than 10, delete the oldest ones
      if (nonFavoriteAnalyses.length > 10) {
        const analysesToDelete = nonFavoriteAnalyses.slice(10)
        const idsToDelete = analysesToDelete.map(a => a.id)

        console.log(`🧹 Cleaning up ${idsToDelete.length} old headline analyses for user ${userId}`)

        const { error } = await supabase
          .from('headline_analyses')
          .delete()
          .in('id', idsToDelete)

        if (error) {
          console.error('Error cleaning up old analyses:', error)
          throw new Error(`Failed to cleanup old analyses: ${error.message}`)
        }

        console.log(`✅ Successfully cleaned up ${idsToDelete.length} old headline analyses`)
      }
    } catch (error) {
      console.error('💥 Exception in cleanupOldAnalyses:', error)
      throw error
    }
  }

}

// Export singleton instance
export const headlineAnalysisService = new HeadlineAnalysisService()

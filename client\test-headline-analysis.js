/**
 * Test script for Headline Analysis functionality
 * Run this in the browser console on the headline analyzer page
 */

console.log('🧪 Starting Headline Analysis Tests...');

// Test 1: Check if services are available
function testServicesAvailable() {
  console.log('\n📋 Test 1: Checking if services are available...');
  
  try {
    // Check if headlineAnalysisService is available
    if (typeof window.headlineAnalysisService !== 'undefined') {
      console.log('✅ headlineAnalysisService is available');
    } else {
      console.log('❌ headlineAnalysisService is not available');
      return false;
    }

    // Check if supabase is available
    if (typeof window.supabase !== 'undefined') {
      console.log('✅ supabase is available');
    } else {
      console.log('❌ supabase is not available');
      return false;
    }

    return true;
  } catch (error) {
    console.error('❌ Error checking services:', error);
    return false;
  }
}

// Test 2: Check authentication
async function testAuthentication() {
  console.log('\n🔐 Test 2: Checking authentication...');
  
  try {
    const { data: { user }, error } = await window.supabase.auth.getUser();
    
    if (error) {
      console.error('❌ Authentication error:', error);
      return false;
    }
    
    if (user) {
      console.log('✅ User is authenticated:', {
        id: user.id,
        email: user.email
      });
      return user;
    } else {
      console.log('⚠️ User is not authenticated');
      return false;
    }
  } catch (error) {
    console.error('❌ Authentication test failed:', error);
    return false;
  }
}

// Test 3: Test database connection
async function testDatabaseConnection(userId) {
  console.log('\n🗄️ Test 3: Testing database connection...');
  
  try {
    const { data, error } = await window.supabase
      .from('headline_analyses')
      .select('id, created_at, headline_text, overall_score')
      .eq('user_id', userId)
      .limit(5);
    
    if (error) {
      console.error('❌ Database query failed:', error);
      return false;
    }
    
    console.log('✅ Database connection successful:', {
      recordsFound: data?.length || 0,
      sampleRecord: data?.[0] || 'No records'
    });
    
    return true;
  } catch (error) {
    console.error('❌ Database connection test failed:', error);
    return false;
  }
}

// Test 4: Test service methods
async function testServiceMethods(userId) {
  console.log('\n🔧 Test 4: Testing service methods...');
  
  try {
    // Test getUserAnalyses
    console.log('Testing getUserAnalyses...');
    const analyses = await window.headlineAnalysisService.getUserAnalyses(userId, {
      limit: 5,
      orderBy: 'created_at',
      orderDirection: 'desc'
    });
    
    console.log('✅ getUserAnalyses successful:', {
      count: analyses.length,
      sample: analyses[0] ? {
        id: analyses[0].id,
        headline: analyses[0].headline_text,
        score: analyses[0].overall_score
      } : 'No analyses'
    });
    
    // Test getUserStats
    console.log('Testing getUserStats...');
    const stats = await window.headlineAnalysisService.getUserStats(userId);
    
    console.log('✅ getUserStats successful:', stats);
    
    return { analyses, stats };
  } catch (error) {
    console.error('❌ Service methods test failed:', error);
    return null;
  }
}

// Test 5: Test tab functionality
function testTabFunctionality() {
  console.log('\n📋 Test 5: Testing tab functionality...');
  
  try {
    // Check if tabs are present
    const tabs = document.querySelectorAll('[role="tablist"] button');
    const tabTexts = Array.from(tabs).map(tab => tab.textContent.trim());
    
    console.log('Tabs found:', tabTexts);
    
    const expectedTabs = ['Analizador de Títulos', 'Historial', 'Favoritos'];
    const hasAllTabs = expectedTabs.every(expectedTab => 
      tabTexts.some(tabText => tabText.includes(expectedTab.split(' ')[0]))
    );
    
    if (hasAllTabs) {
      console.log('✅ All tabs are present');
      return true;
    } else {
      console.log('❌ Some tabs are missing');
      return false;
    }
  } catch (error) {
    console.error('❌ Tab functionality test failed:', error);
    return false;
  }
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Running all Headline Analysis tests...\n');
  
  const results = {
    servicesAvailable: false,
    authentication: false,
    databaseConnection: false,
    serviceMethods: false,
    tabFunctionality: false
  };
  
  // Test 1: Services available
  results.servicesAvailable = testServicesAvailable();
  
  if (!results.servicesAvailable) {
    console.log('\n❌ Cannot continue tests - services not available');
    return results;
  }
  
  // Test 2: Authentication
  const user = await testAuthentication();
  results.authentication = !!user;
  
  if (!user) {
    console.log('\n⚠️ Some tests will be skipped - user not authenticated');
  } else {
    // Test 3: Database connection
    results.databaseConnection = await testDatabaseConnection(user.id);
    
    // Test 4: Service methods
    const serviceResults = await testServiceMethods(user.id);
    results.serviceMethods = !!serviceResults;
  }
  
  // Test 5: Tab functionality
  results.tabFunctionality = testTabFunctionality();
  
  // Summary
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
  });
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Headline Analysis implementation is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Please check the implementation.');
  }
  
  return results;
}

// Export functions for manual testing
window.headlineAnalysisTests = {
  runAllTests,
  testServicesAvailable,
  testAuthentication,
  testDatabaseConnection,
  testServiceMethods,
  testTabFunctionality
};

console.log('✅ Test script loaded. Run window.headlineAnalysisTests.runAllTests() to start testing.');

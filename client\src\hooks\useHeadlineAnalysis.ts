import { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useAuth } from '@/hooks/use-auth'
import { headlineAnalysisService } from '@/services/headlineAnalysisService'
import { useToast } from '@/hooks/use-toast'
import type { HeadlineAnalysis, CreateHeadlineAnalysisData, UpdateHeadlineAnalysisData } from '@/lib/supabase'

/**
 * Hook for managing headline analysis operations
 * Following the exact same patterns as useDesignAnalysis.ts
 */
export function useHeadlineAnalysis() {
  const { user } = useAuth()
  const { toast } = useToast()
  const queryClient = useQueryClient()

  // Query for getting user's analyses
  const {
    data: analyses = [],
    isLoading: isLoadingAnalyses,
    error: analysesError,
    refetch: refetchAnalyses
  } = useQuery({
    queryKey: ['headline-analyses', user?.id],
    queryFn: async () => {
      console.log('🔍 useHeadlineAnalysis: Fetching analyses for user:', user?.id)
      try {
        const result = await headlineAnalysisService.getUserAnalyses(user?.id || '', {
          limit: 100,
          orderBy: 'created_at',
          orderDirection: 'desc'
        })
        console.log('✅ useHeadlineAnalysis: Successfully fetched analyses:', result.length)
        return result
      } catch (error) {
        console.error('❌ useHeadlineAnalysis: Error fetching analyses:', error)
        throw error
      }
    },
    enabled: !!user?.id && user.id !== 'anonymous',
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes
  })

  // Query for getting recent analyses (history)
  const {
    data: recentAnalyses = [],
    isLoading: isLoadingRecent,
    refetch: refetchRecent
  } = useQuery({
    queryKey: ['headline-analyses-recent', user?.id],
    queryFn: async () => {
      console.log('🔍 useHeadlineAnalysis: Fetching recent analyses for user:', user?.id)
      try {
        const result = await headlineAnalysisService.getRecentAnalyses(user?.id || '')
        console.log('✅ useHeadlineAnalysis: Successfully fetched recent analyses:', result.length)
        return result
      } catch (error) {
        console.error('❌ useHeadlineAnalysis: Error fetching recent analyses:', error)
        throw error
      }
    },
    enabled: !!user?.id && user.id !== 'anonymous',
    staleTime: 1000 * 60 * 2, // 2 minutes
    gcTime: 1000 * 60 * 10, // 10 minutes
  })

  // Query for getting favorite analyses
  const {
    data: favoriteAnalyses = [],
    isLoading: isLoadingFavorites,
    refetch: refetchFavorites
  } = useQuery({
    queryKey: ['headline-analyses-favorites', user?.id],
    queryFn: async () => {
      console.log('🔍 useHeadlineAnalysis: Fetching favorite analyses for user:', user?.id)
      try {
        const result = await headlineAnalysisService.getFavoriteAnalyses(user?.id || '')
        console.log('✅ useHeadlineAnalysis: Successfully fetched favorite analyses:', result.length)
        return result
      } catch (error) {
        console.error('❌ useHeadlineAnalysis: Error fetching favorite analyses:', error)
        throw error
      }
    },
    enabled: !!user?.id && user.id !== 'anonymous',
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes
  })

  // Query for user statistics
  const {
    data: userStats,
    isLoading: isLoadingStats,
    refetch: refetchStats
  } = useQuery({
    queryKey: ['headline-analysis-stats', user?.id],
    queryFn: async () => {
      console.log('🔍 useHeadlineAnalysis: Fetching stats for user:', user?.id)
      try {
        const result = await headlineAnalysisService.getUserStats(user?.id || '')
        console.log('✅ useHeadlineAnalysis: Successfully fetched stats:', result)
        return result
      } catch (error) {
        console.error('❌ useHeadlineAnalysis: Error fetching stats:', error)
        throw error
      }
    },
    enabled: !!user?.id && user.id !== 'anonymous',
    staleTime: 1000 * 60 * 10, // 10 minutes
    gcTime: 1000 * 60 * 60, // 1 hour
  })

  // Mutation for saving analysis
  const saveAnalysisMutation = useMutation({
    mutationFn: (analysisData: CreateHeadlineAnalysisData) =>
      headlineAnalysisService.saveAnalysis(analysisData),
    onSuccess: async (savedAnalysis) => {
      // Invalidate and refetch all related queries
      queryClient.invalidateQueries({ queryKey: ['headline-analyses'] })
      queryClient.invalidateQueries({ queryKey: ['headline-analysis-stats'] })
      
      // Clean up old analyses after saving
      try {
        await headlineAnalysisService.cleanupOldAnalyses(user?.id || '')
        // Refetch recent analyses after cleanup
        queryClient.invalidateQueries({ queryKey: ['headline-analyses-recent'] })
      } catch (error) {
        console.error('Error cleaning up old analyses:', error)
        // Don't show error to user as this is background cleanup
      }

      toast({
        title: "Análisis guardado",
        description: "Tu análisis de título ha sido guardado exitosamente",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error al guardar",
        description: error.message,
        variant: "destructive",
      })
    }
  })

  // Mutation for updating analysis
  const updateAnalysisMutation = useMutation({
    mutationFn: (updateData: UpdateHeadlineAnalysisData) =>
      headlineAnalysisService.updateAnalysis(updateData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['headline-analyses'] })
      queryClient.invalidateQueries({ queryKey: ['headline-analysis-stats'] })
    },
    onError: (error: Error) => {
      toast({
        title: "Error al actualizar",
        description: error.message,
        variant: "destructive",
      })
    }
  })

  // Mutation for deleting analysis
  const deleteAnalysisMutation = useMutation({
    mutationFn: (id: string) => headlineAnalysisService.deleteAnalysis(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['headline-analyses'] })
      queryClient.invalidateQueries({ queryKey: ['headline-analysis-stats'] })
      toast({
        title: "Análisis eliminado",
        description: "El análisis ha sido eliminado exitosamente",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error al eliminar",
        description: error.message,
        variant: "destructive",
      })
    }
  })

  // Mutation for toggling favorite
  const toggleFavoriteMutation = useMutation({
    mutationFn: (id: string) => headlineAnalysisService.toggleFavorite(id),
    onSuccess: (updatedAnalysis) => {
      queryClient.invalidateQueries({ queryKey: ['headline-analyses'] })
      queryClient.invalidateQueries({ queryKey: ['headline-analysis-stats'] })
      
      const action = updatedAnalysis.is_favorite ? "agregado a" : "removido de"
      toast({
        title: `Análisis ${action} favoritos`,
        description: updatedAnalysis.is_favorite 
          ? "El análisis se ha guardado en favoritos" 
          : "El análisis se ha removido de favoritos",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error al actualizar favoritos",
        description: error.message,
        variant: "destructive",
      })
    }
  })

  // Mutation for updating tags
  const updateTagsMutation = useMutation({
    mutationFn: ({ id, tags }: { id: string; tags: string[] }) =>
      headlineAnalysisService.updateTags(id, tags),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['headline-analyses'] })
      toast({
        title: "Etiquetas actualizadas",
        description: "Las etiquetas del análisis han sido actualizadas",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error al actualizar etiquetas",
        description: error.message,
        variant: "destructive",
      })
    }
  })

  // Mutation for updating notes
  const updateNotesMutation = useMutation({
    mutationFn: ({ id, notes }: { id: string; notes: string }) =>
      headlineAnalysisService.updateNotes(id, notes),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['headline-analyses'] })
      toast({
        title: "Notas actualizadas",
        description: "Las notas del análisis han sido actualizadas",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error al actualizar notas",
        description: error.message,
        variant: "destructive",
      })
    }
  })

  return {
    // Data
    analyses,
    recentAnalyses,
    favoriteAnalyses,
    userStats,

    // Loading states
    isLoadingAnalyses,
    isLoadingRecent,
    isLoadingFavorites,
    isLoadingStats,

    // Errors
    analysesError,

    // Mutations
    saveAnalysis: saveAnalysisMutation.mutate,
    updateAnalysis: updateAnalysisMutation.mutate,
    deleteAnalysis: deleteAnalysisMutation.mutate,
    toggleFavorite: toggleFavoriteMutation.mutate,
    updateTags: updateTagsMutation.mutate,
    updateNotes: updateNotesMutation.mutate,

    // Mutation states
    isSaving: saveAnalysisMutation.isPending,
    isUpdating: updateAnalysisMutation.isPending,
    isDeleting: deleteAnalysisMutation.isPending,
    isTogglingFavorite: toggleFavoriteMutation.isPending,

    // Refetch functions
    refetchAnalyses,
    refetchRecent,
    refetchFavorites,
    refetchStats,
  }
}

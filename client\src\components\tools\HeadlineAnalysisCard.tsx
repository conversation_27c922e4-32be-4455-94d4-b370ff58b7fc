import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Heart,
  Edit3,
  Trash2,
  Calendar,
  Eye,
  RefreshCw,
  Check,
  X,
  FileText,
  BarChart3,
  MessageSquare,
  Target,
  Clock
} from 'lucide-react';
import type { HeadlineAnalysis } from '@/lib/supabase';
import { Badge } from '@/components/ui/badge';

// Component for displaying a saved headline analysis card
interface HeadlineAnalysisCardProps {
  analysis: HeadlineAnalysis;
  onLoad: () => void;
  onToggleFavorite: () => void;
  onRename: (newName: string) => void;
  onDelete: () => void;
  onRegenerate: () => void;
  isRenaming: boolean;
  onStartRename: () => void;
  onCancelRename: () => void;
  renameValue: string;
  onRenameValueChange: (value: string) => void;
}

function HeadlineAnalysisCard({
  analysis,
  onLoad,
  onToggleFavorite,
  onRename,
  onDelete,
  onRegenerate,
  isRenaming,
  onStartRename,
  onCancelRename,
  renameValue,
  onRenameValueChange,
}: HeadlineAnalysisCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get score color based on value
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-50 border-green-200';
    if (score >= 60) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-red-600 bg-red-50 border-red-200';
  };

  // Get content type display name
  const getContentTypeDisplay = (contentType: string) => {
    const types: Record<string, string> = {
      'blog': 'Blog',
      'social': 'Redes Sociales',
      'email': 'Email',
      'ad': 'Publicidad',
      'news': 'Noticias',
      'video': 'Video',
      'podcast': 'Podcast'
    };
    return types[contentType] || contentType;
  };

  // Truncate headline for display
  const truncateHeadline = (headline: string, maxLength: number = 60) => {
    if (headline.length <= maxLength) return headline;
    return headline.substring(0, maxLength) + '...';
  };

  return (
    <Card className="p-4 hover:shadow-md transition-all duration-200 border border-gray-200">
      <div className="space-y-3">
        {/* Header with title and actions */}
        <div className="flex items-start justify-between gap-3">
          <div className="flex-1 min-w-0">
            {isRenaming ? (
              <div className="flex items-center gap-2">
                <Input
                  value={renameValue}
                  onChange={(e) => onRenameValueChange(e.target.value)}
                  className="text-sm"
                  placeholder="Nombre del análisis"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      onRename(renameValue);
                    } else if (e.key === 'Escape') {
                      onCancelRename();
                    }
                  }}
                  autoFocus
                />
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => onRename(renameValue)}
                  className="p-1 h-8 w-8"
                >
                  <Check className="h-4 w-4 text-green-600" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={onCancelRename}
                  className="p-1 h-8 w-8"
                >
                  <X className="h-4 w-4 text-red-600" />
                </Button>
              </div>
            ) : (
              <div>
                <h3 className="font-medium text-gray-900 text-sm leading-tight">
                  {analysis.custom_name || `Análisis ${formatDate(analysis.created_at)}`}
                </h3>
                <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                  "{truncateHeadline(analysis.headline_text)}"
                </p>
              </div>
            )}
          </div>

          {/* Action buttons */}
          <div className="flex items-center gap-1 flex-shrink-0">
            <Button
              size="sm"
              variant="ghost"
              onClick={onToggleFavorite}
              className={`p-1 h-8 w-8 ${analysis.is_favorite ? 'text-red-500 hover:text-red-600' : 'text-gray-400 hover:text-red-500'}`}
            >
              <Heart className={`h-4 w-4 ${analysis.is_favorite ? 'fill-current' : ''}`} />
            </Button>
            
            <Button
              size="sm"
              variant="ghost"
              onClick={onStartRename}
              className="p-1 h-8 w-8 text-gray-400 hover:text-blue-600"
            >
              <Edit3 className="h-4 w-4" />
            </Button>
            
            <Button
              size="sm"
              variant="ghost"
              onClick={onDelete}
              className="p-1 h-8 w-8 text-gray-400 hover:text-red-600"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Headline preview */}
        <div className="bg-gray-50 rounded-lg p-3 border border-gray-100">
          <div className="flex items-start gap-2">
            <FileText className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 break-words">
                {isExpanded ? analysis.headline_text : truncateHeadline(analysis.headline_text, 80)}
              </p>
              {analysis.headline_text.length > 80 && (
                <button
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="text-xs text-blue-600 hover:text-blue-700 mt-1"
                >
                  {isExpanded ? 'Ver menos' : 'Ver más'}
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Metadata and score */}
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>{formatDate(analysis.created_at)}</span>
            </div>
            
            <div className="flex items-center gap-1">
              <Target className="h-3 w-3" />
              <span>{getContentTypeDisplay(analysis.content_type)}</span>
            </div>

            {analysis.audience_context && (
              <div className="flex items-center gap-1">
                <MessageSquare className="h-3 w-3" />
                <span className="truncate max-w-20">{analysis.audience_context}</span>
              </div>
            )}

            {analysis.view_count > 0 && (
              <div className="flex items-center gap-1">
                <Eye className="h-3 w-3" />
                <span>{analysis.view_count}</span>
              </div>
            )}

            {analysis.processing_time && (
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>{analysis.processing_time.toFixed(1)}s</span>
              </div>
            )}
          </div>

          {/* Score badge */}
          <Badge 
            variant="outline" 
            className={`text-xs font-medium ${getScoreColor(analysis.overall_score)}`}
          >
            <BarChart3 className="h-3 w-3 mr-1" />
            {analysis.overall_score}/100
          </Badge>
        </div>

        {/* Tags if present */}
        {analysis.tags && analysis.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {analysis.tags.slice(0, 3).map((tag, index) => (
              <Badge key={index} variant="secondary" className="text-xs px-2 py-0.5">
                {tag}
              </Badge>
            ))}
            {analysis.tags.length > 3 && (
              <Badge variant="secondary" className="text-xs px-2 py-0.5">
                +{analysis.tags.length - 3}
              </Badge>
            )}
          </div>
        )}

        {/* Action buttons */}
        <div className="flex items-center gap-2 pt-2 border-t border-gray-100">
          <Button
            size="sm"
            onClick={onLoad}
            className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Eye className="h-4 w-4 mr-1" />
            Ver análisis
          </Button>
          
          <Button
            size="sm"
            variant="outline"
            onClick={onRegenerate}
            className="px-3"
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </Card>
  );
}

export default HeadlineAnalysisCard;

-- Tabla para almacenar las marcas
CREATE TABLE marcas (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  
  -- Información básica
  brand_name TEXT NOT NULL,
  website TEXT,
  industry TEXT NOT NULL,
  
  -- Identidad visual
  logo_url TEXT,
  primary_color TEXT NOT NULL DEFAULT '#3018ef',
  secondary_color TEXT NOT NULL DEFAULT '#dd3a5a',
  
  -- Audiencia y tono
  target_audience TEXT NOT NULL,
  tone TEXT NOT NULL,
  personality TEXT[] DEFAULT '{}',
  
  -- Posicionamiento
  description TEXT NOT NULL,
  unique_value TEXT NOT NULL,
  competitors TEXT,
  
  -- Documentos y ejemplos
  documents TEXT[] DEFAULT '{}',
  examples TEXT,
  
  -- Metadata
  status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'archived')),
  campaigns_count INTEGER DEFAULT 0,
  assets_count INTEGER DEFAULT 0,
  
  -- Usuario (opcional para multi-tenant)
  user_id TEXT
);

-- Índices para mejorar performance
CREATE INDEX idx_nucleos_user_id ON nucleos(user_id);
CREATE INDEX idx_nucleos_status ON nucleos(status);
CREATE INDEX idx_nucleos_updated_at ON nucleos(updated_at DESC);
CREATE INDEX idx_nucleos_brand_name ON nucleos(brand_name);

-- Función para actualizar updated_at automáticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger para actualizar updated_at
CREATE TRIGGER update_nucleos_updated_at 
    BEFORE UPDATE ON nucleos 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Habilitar Row Level Security (RLS)
ALTER TABLE nucleos ENABLE ROW LEVEL SECURITY;

-- Política para que los usuarios solo vean sus propios núcleos
CREATE POLICY "Users can view their own nucleos" ON nucleos
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own nucleos" ON nucleos
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own nucleos" ON nucleos
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own nucleos" ON nucleos
    FOR DELETE USING (auth.uid()::text = user_id);

-- ===============================================
-- DESIGN TOOLS TABLES
-- ===============================================

-- Tabla para almacenar análisis de complejidad visual
CREATE TABLE design_analyses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,

  -- Usuario propietario
  user_id TEXT NOT NULL,

  -- Información del archivo analizado
  original_filename TEXT NOT NULL,
  file_size INTEGER,
  file_type TEXT,
  file_url TEXT, -- URL del archivo almacenado

  -- Parámetros de análisis
  tool_type TEXT NOT NULL DEFAULT 'visual_complexity_analyzer',
  analysis_version TEXT DEFAULT '1.0',

  -- Resultados del análisis
  overall_score INTEGER NOT NULL,
  complexity_scores JSONB NOT NULL, -- {color: 5, layout: 7, typography: 6, etc.}
  analysis_areas JSONB NOT NULL, -- Array de áreas analizadas con scores y descripciones
  recommendations JSONB NOT NULL, -- Array de recomendaciones categorizadas

  -- Análisis detallado de IA
  ai_analysis_summary TEXT,
  gemini_analysis TEXT,
  agent_message TEXT,
  visuai_insights JSONB,

  -- Metadata adicional
  analysis_duration_ms INTEGER,
  status TEXT NOT NULL DEFAULT 'completed' CHECK (status IN ('processing', 'completed', 'failed')),
  error_message TEXT,

  -- Gestión de favoritos y organización
  is_favorite BOOLEAN DEFAULT FALSE,
  custom_name TEXT, -- Nombre personalizado asignado por el usuario
  tags TEXT[] DEFAULT '{}',
  notes TEXT,

  -- Estadísticas de uso
  view_count INTEGER DEFAULT 0,
  last_viewed_at TIMESTAMP WITH TIME ZONE,
  regeneration_count INTEGER DEFAULT 0 -- Número de veces que se ha regenerado
);

-- Tabla para almacenar análisis de títulos/headlines
CREATE TABLE headline_analyses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,

  -- Usuario propietario
  user_id TEXT NOT NULL,

  -- Información del título analizado
  headline_text TEXT NOT NULL,
  content_type TEXT NOT NULL DEFAULT 'blog',
  audience_context TEXT,
  content_type_context TEXT,

  -- Parámetros de análisis
  tool_type TEXT NOT NULL DEFAULT 'headline_analyzer',
  analysis_version TEXT DEFAULT '1.0',

  -- Resultados del análisis
  overall_score INTEGER NOT NULL,
  basic_analysis JSONB NOT NULL, -- {word_count, char_count, contains_number, is_question, quality_check}
  advanced_analysis JSONB NOT NULL, -- {emotional_impact, clarity, engagement_potential, etc.}
  recommendations JSONB NOT NULL, -- Array de recomendaciones categorizadas

  -- Análisis detallado de IA
  ai_analysis_summary TEXT,
  gemini_analysis TEXT,
  agent_message TEXT,

  -- Metadata adicional
  analysis_duration_ms INTEGER,
  processing_time FLOAT,
  status TEXT NOT NULL DEFAULT 'completed' CHECK (status IN ('processing', 'completed', 'failed')),
  error_message TEXT,

  -- Gestión de favoritos y organización
  is_favorite BOOLEAN DEFAULT FALSE,
  custom_name TEXT, -- Nombre personalizado asignado por el usuario
  tags TEXT[] DEFAULT '{}',
  notes TEXT,

  -- Estadísticas de uso
  view_count INTEGER DEFAULT 0,
  last_viewed_at TIMESTAMP WITH TIME ZONE,
  regeneration_count INTEGER DEFAULT 0 -- Número de veces que se ha regenerado
);

-- Tabla para almacenar archivos subidos por usuarios (para contexto)
CREATE TABLE design_uploads (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,

  -- Usuario propietario
  user_id TEXT NOT NULL,

  -- Información del archivo
  original_filename TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  file_type TEXT NOT NULL,
  file_url TEXT NOT NULL, -- URL del archivo en storage
  file_hash TEXT, -- Hash para evitar duplicados

  -- Metadata
  upload_source TEXT DEFAULT 'design_complexity_analyzer',
  is_processed BOOLEAN DEFAULT FALSE,

  -- Relación con análisis
  analysis_id UUID REFERENCES design_analyses(id) ON DELETE SET NULL
);

-- Índices para mejorar performance
CREATE INDEX idx_design_analyses_user_id ON design_analyses(user_id);
CREATE INDEX idx_design_analyses_created_at ON design_analyses(created_at DESC);
CREATE INDEX idx_design_analyses_tool_type ON design_analyses(tool_type);
CREATE INDEX idx_design_analyses_status ON design_analyses(status);
CREATE INDEX idx_design_analyses_is_favorite ON design_analyses(user_id, is_favorite) WHERE is_favorite = true;
CREATE INDEX idx_design_analyses_overall_score ON design_analyses(overall_score);
CREATE INDEX idx_design_analyses_view_count ON design_analyses(view_count);
CREATE INDEX idx_design_analyses_last_viewed_at ON design_analyses(last_viewed_at DESC);

CREATE INDEX idx_design_uploads_user_id ON design_uploads(user_id);
CREATE INDEX idx_design_uploads_file_hash ON design_uploads(file_hash);
CREATE INDEX idx_design_uploads_analysis_id ON design_uploads(analysis_id);

-- Triggers para actualizar updated_at
CREATE TRIGGER update_design_analyses_updated_at
    BEFORE UPDATE ON design_analyses
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Habilitar Row Level Security (RLS)
ALTER TABLE design_analyses ENABLE ROW LEVEL SECURITY;
ALTER TABLE design_uploads ENABLE ROW LEVEL SECURITY;
ALTER TABLE headline_analyses ENABLE ROW LEVEL SECURITY;

-- Políticas RLS para design_analyses
CREATE POLICY "Users can view their own design analyses" ON design_analyses
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own design analyses" ON design_analyses
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own design analyses" ON design_analyses
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own design analyses" ON design_analyses
    FOR DELETE USING (auth.uid()::text = user_id);

-- Políticas RLS para design_uploads
CREATE POLICY "Users can view their own design uploads" ON design_uploads
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own design uploads" ON design_uploads
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own design uploads" ON design_uploads
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own design uploads" ON design_uploads
    FOR DELETE USING (auth.uid()::text = user_id);

-- Políticas RLS para headline_analyses
CREATE POLICY "Users can view their own headline analyses" ON headline_analyses
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own headline analyses" ON headline_analyses
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own headline analyses" ON headline_analyses
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own headline analyses" ON headline_analyses
    FOR DELETE USING (auth.uid()::text = user_id);

-- ===============================================
-- FOCUS GROUP SIMULATOR TABLES
-- ===============================================

-- Tabla para almacenar simulaciones de focus group
CREATE TABLE focus_group_simulations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,

  -- Usuario propietario
  user_id TEXT NOT NULL,

  -- Parámetros de entrada de la simulación
  content TEXT NOT NULL, -- Contenido a analizar
  product_category TEXT, -- Categoría del producto (opcional)
  context TEXT, -- Contexto adicional (opcional)
  custom_questions JSONB DEFAULT '[]'::jsonb, -- Preguntas personalizadas
  num_participants INTEGER DEFAULT 6, -- Número de participantes
  discussion_rounds INTEGER DEFAULT 3, -- Rondas de discusión

  -- Resultados de la simulación
  simulation_results JSONB NOT NULL, -- Resultados completos de la simulación
  participants JSONB NOT NULL, -- Array de participantes generados
  discussions JSONB NOT NULL, -- Array de discusiones
  summary JSONB NOT NULL, -- Resumen con insights, sentimientos, patrones, recomendaciones

  -- Metadata de la simulación
  simulation_duration_ms INTEGER, -- Duración de la simulación en ms
  status TEXT NOT NULL DEFAULT 'completed' CHECK (status IN ('processing', 'completed', 'failed')),
  error_message TEXT,

  -- Gestión de favoritos y organización
  is_favorite BOOLEAN DEFAULT FALSE,
  custom_name TEXT, -- Nombre personalizado asignado por el usuario
  tags TEXT[] DEFAULT '{}',
  notes TEXT,

  -- Estadísticas de uso
  view_count INTEGER DEFAULT 0,
  last_viewed_at TIMESTAMP WITH TIME ZONE,
  regeneration_count INTEGER DEFAULT 0 -- Número de veces que se ha regenerado
);

-- Índices para optimizar consultas
CREATE INDEX idx_focus_group_simulations_user_id ON focus_group_simulations(user_id);
CREATE INDEX idx_focus_group_simulations_created_at ON focus_group_simulations(created_at DESC);
CREATE INDEX idx_focus_group_simulations_is_favorite ON focus_group_simulations(user_id, is_favorite) WHERE is_favorite = true;
CREATE INDEX idx_focus_group_simulations_status ON focus_group_simulations(status);

-- Trigger para actualizar updated_at
CREATE TRIGGER update_focus_group_simulations_updated_at
    BEFORE UPDATE ON focus_group_simulations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Habilitar Row Level Security (RLS)
ALTER TABLE focus_group_simulations ENABLE ROW LEVEL SECURITY;

-- Políticas RLS para focus_group_simulations
CREATE POLICY "Users can view their own focus group simulations" ON focus_group_simulations
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own focus group simulations" ON focus_group_simulations
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own focus group simulations" ON focus_group_simulations
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own focus group simulations" ON focus_group_simulations
    FOR DELETE USING (auth.uid()::text = user_id);

-- ===============================================
-- MOODBOARD TABLES
-- ===============================================

-- Tabla para almacenar moodboards interactivos
CREATE TABLE moodboards (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,

  -- Usuario propietario
  user_id TEXT NOT NULL,

  -- Información básica del moodboard
  title TEXT NOT NULL DEFAULT 'Untitled Moodboard',
  description TEXT,

  -- Configuración del moodboard
  tool_type TEXT NOT NULL DEFAULT 'interactive_moodboard',
  version TEXT DEFAULT '1.0',

  -- Datos del canvas de Tldraw
  tldraw_data JSONB, -- Datos completos del estado de Tldraw
  canvas_snapshot TEXT, -- URL de imagen snapshot del canvas

  -- Metadatos del proyecto
  tags TEXT[] DEFAULT '{}',
  is_public BOOLEAN DEFAULT FALSE,
  is_favorite BOOLEAN DEFAULT FALSE,

  -- Configuración de colaboración
  collaboration_enabled BOOLEAN DEFAULT FALSE,
  shared_with TEXT[] DEFAULT '{}', -- Array de user_ids con acceso

  -- Estadísticas de uso
  view_count INTEGER DEFAULT 0,
  last_viewed_at TIMESTAMP WITH TIME ZONE,

  -- Estado del proyecto
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'archived', 'deleted')),

  -- Notas adicionales
  notes TEXT
);

-- Tabla para almacenar historial de cambios en moodboards
CREATE TABLE moodboard_history (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,

  -- Referencia al moodboard
  moodboard_id UUID NOT NULL REFERENCES moodboards(id) ON DELETE CASCADE,
  user_id TEXT NOT NULL,

  -- Datos del cambio
  change_type TEXT NOT NULL CHECK (change_type IN ('create', 'update', 'snapshot')),
  change_description TEXT,
  tldraw_data_snapshot JSONB,

  -- Metadatos del cambio
  version_number INTEGER DEFAULT 1,
  is_auto_save BOOLEAN DEFAULT TRUE
);

-- Índices para mejorar performance
CREATE INDEX idx_moodboards_user_id ON moodboards(user_id);
CREATE INDEX idx_moodboards_created_at ON moodboards(created_at DESC);
CREATE INDEX idx_moodboards_updated_at ON moodboards(updated_at DESC);
CREATE INDEX idx_moodboards_tool_type ON moodboards(tool_type);
CREATE INDEX idx_moodboards_status ON moodboards(status);
CREATE INDEX idx_moodboards_is_favorite ON moodboards(is_favorite);
CREATE INDEX idx_moodboards_is_public ON moodboards(is_public);
CREATE INDEX idx_moodboards_tags ON moodboards USING GIN(tags);

CREATE INDEX idx_moodboard_history_moodboard_id ON moodboard_history(moodboard_id);
CREATE INDEX idx_moodboard_history_user_id ON moodboard_history(user_id);
CREATE INDEX idx_moodboard_history_created_at ON moodboard_history(created_at DESC);
CREATE INDEX idx_moodboard_history_change_type ON moodboard_history(change_type);

-- Triggers para actualizar updated_at
CREATE TRIGGER update_moodboards_updated_at
    BEFORE UPDATE ON moodboards
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Habilitar Row Level Security (RLS)
ALTER TABLE moodboards ENABLE ROW LEVEL SECURITY;
ALTER TABLE moodboard_history ENABLE ROW LEVEL SECURITY;

-- Políticas RLS para moodboards
CREATE POLICY "Users can view their own moodboards" ON moodboards
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can view public moodboards" ON moodboards
    FOR SELECT USING (is_public = true);

CREATE POLICY "Users can insert their own moodboards" ON moodboards
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own moodboards" ON moodboards
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own moodboards" ON moodboards
    FOR DELETE USING (auth.uid()::text = user_id);

-- Políticas RLS para moodboard_history
CREATE POLICY "Users can view their own moodboard history" ON moodboard_history
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own moodboard history" ON moodboard_history
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own moodboard history" ON moodboard_history
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own moodboard history" ON moodboard_history
    FOR DELETE USING (auth.uid()::text = user_id);

-- ===============================================
-- USER COLOR PALETTES TABLE
-- ===============================================

-- Tabla para almacenar paletas de colores definidas por el usuario
CREATE TABLE user_palettes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,

  -- Usuario propietario
  user_id TEXT NOT NULL,

  -- Información de la paleta
  name TEXT NOT NULL,
  colors JSONB NOT NULL, -- Array de códigos de color hex: ["#FF5733", "#33FF57", "#3357FF"]

  -- Metadata adicional
  description TEXT,
  tags TEXT[] DEFAULT '{}',
  is_favorite BOOLEAN DEFAULT FALSE,

  -- Validaciones
  CONSTRAINT valid_palette_name CHECK (LENGTH(name) >= 1 AND LENGTH(name) <= 100),
  CONSTRAINT valid_colors_array CHECK (jsonb_array_length(colors) >= 1 AND jsonb_array_length(colors) <= 20)
);

-- Índices para optimizar consultas
CREATE INDEX idx_user_palettes_user_id ON user_palettes(user_id);
CREATE INDEX idx_user_palettes_created_at ON user_palettes(created_at DESC);
CREATE INDEX idx_user_palettes_name ON user_palettes(name);
CREATE INDEX idx_user_palettes_is_favorite ON user_palettes(is_favorite) WHERE is_favorite = true;

-- Trigger para actualizar updated_at
CREATE TRIGGER update_user_palettes_updated_at
    BEFORE UPDATE ON user_palettes
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Habilitar Row Level Security (RLS)
ALTER TABLE user_palettes ENABLE ROW LEVEL SECURITY;

-- Políticas RLS para user_palettes
CREATE POLICY "Users can view their own palettes" ON user_palettes
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own palettes" ON user_palettes
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own palettes" ON user_palettes
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own palettes" ON user_palettes
    FOR DELETE USING (auth.uid()::text = user_id);

-- Insertar algunos datos de ejemplo (opcional)
INSERT INTO marcas (
  brand_name,
  industry,
  target_audience,
  tone,
  description,
  unique_value,
  personality,
  status
) VALUES
(
  'Emma Studio',
  'SaaS & Tecnología',
  'Marketers, agencias de marketing, emprendedores que buscan automatizar y mejorar sus estrategias de marketing con IA',
  'Innovador y Visionario',
  'Plataforma de marketing con IA revolucionaria que permite a las marcas ejecutar campañas inteligentes sin empezar desde cero',
  'Emma aprende la identidad de tu marca una sola vez y ejecuta con ese contexto en todas las herramientas, eliminando la necesidad de repetir briefs',
  ARRAY['Innovadora', 'Confiable', 'Moderna', 'Experta'],
  'active'
),
(
  'TechFlow Solutions',
  'Tecnología',
  'CTOs, desarrolladores, empresas tecnológicas que buscan soluciones escalables',
  'Técnico y Preciso',
  'Soluciones tecnológicas avanzadas para empresas que buscan optimizar sus procesos digitales',
  'Combinamos expertise técnico con innovación para crear soluciones que realmente escalan',
  ARRAY['Técnica', 'Confiable', 'Innovadora', 'Experta'],
  'active'
);

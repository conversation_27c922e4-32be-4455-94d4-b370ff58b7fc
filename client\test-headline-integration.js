/**
 * Integration test for Headline Analyzer functionality
 * Tests both original analysis functionality and new history/favorites system
 * Run this in the browser console on the headline analyzer page
 */

console.log('🧪 Starting Headline Analyzer Integration Tests...');

// Test configuration
const TEST_HEADLINE = "10 Secretos para Aumentar tus Ventas Online en 2024";
const TEST_CONTENT_TYPE = "blog";
const TEST_AUDIENCE = "emprendedores";

// Test 1: Check UI structure and tabs
function testUIStructure() {
  console.log('\n📋 Test 1: Checking UI structure and tabs...');
  
  try {
    // Check if main tabs are present
    const mainTabs = document.querySelectorAll('[role="tablist"] button');
    const mainTabTexts = Array.from(mainTabs).map(tab => tab.textContent.trim());
    
    console.log('Main tabs found:', mainTabTexts);
    
    const expectedMainTabs = ['Analizador de Títulos', 'Historial', 'Favoritos'];
    const hasAllMainTabs = expectedMainTabs.every(expectedTab => 
      mainTabTexts.some(tabText => tabText.includes(expectedTab.split(' ')[0]))
    );
    
    if (hasAllMainTabs) {
      console.log('✅ All main tabs are present');
    } else {
      console.log('❌ Some main tabs are missing');
      return false;
    }
    
    // Check if form elements are present
    const headlineInput = document.querySelector('#headline');
    const analyzeButton = document.querySelector('button[onclick*="analyzeHeadline"], button:contains("Analizar título")');
    const contentTypeSelect = document.querySelector('select, [role="combobox"]');
    
    if (headlineInput) {
      console.log('✅ Headline input field found');
    } else {
      console.log('❌ Headline input field not found');
      return false;
    }
    
    // Check two-panel layout
    const panels = document.querySelectorAll('.md\\:w-2\\/5, .md\\:w-3\\/5');
    if (panels.length >= 2) {
      console.log('✅ Two-panel layout preserved');
    } else {
      console.log('❌ Two-panel layout not found');
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('❌ UI structure test failed:', error);
    return false;
  }
}

// Test 2: Test form functionality
function testFormFunctionality() {
  console.log('\n📝 Test 2: Testing form functionality...');
  
  try {
    // Find form elements
    const headlineInput = document.querySelector('#headline');
    const analyzeButton = document.querySelector('button');
    
    if (!headlineInput) {
      console.log('❌ Headline input not found');
      return false;
    }
    
    // Test input functionality
    headlineInput.value = TEST_HEADLINE;
    headlineInput.dispatchEvent(new Event('input', { bubbles: true }));
    
    if (headlineInput.value === TEST_HEADLINE) {
      console.log('✅ Headline input works correctly');
    } else {
      console.log('❌ Headline input not working');
      return false;
    }
    
    // Check if analyze button is enabled
    const buttons = Array.from(document.querySelectorAll('button'));
    const analyzeBtn = buttons.find(btn => btn.textContent.includes('Analizar'));
    
    if (analyzeBtn && !analyzeBtn.disabled) {
      console.log('✅ Analyze button is enabled and ready');
    } else {
      console.log('❌ Analyze button not found or disabled');
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('❌ Form functionality test failed:', error);
    return false;
  }
}

// Test 3: Test tab switching
function testTabSwitching() {
  console.log('\n🔄 Test 3: Testing tab switching...');
  
  try {
    // Find tab buttons
    const tabs = document.querySelectorAll('[role="tablist"] button');
    
    if (tabs.length < 3) {
      console.log('❌ Not enough tabs found');
      return false;
    }
    
    // Test switching to history tab
    const historyTab = Array.from(tabs).find(tab => 
      tab.textContent.includes('Historial')
    );
    
    if (historyTab) {
      historyTab.click();
      setTimeout(() => {
        const historyContent = document.querySelector('[value="history"]');
        if (historyContent) {
          console.log('✅ History tab switching works');
        } else {
          console.log('⚠️ History tab content not visible');
        }
      }, 100);
    }
    
    // Test switching to favorites tab
    const favoritesTab = Array.from(tabs).find(tab => 
      tab.textContent.includes('Favoritos')
    );
    
    if (favoritesTab) {
      favoritesTab.click();
      setTimeout(() => {
        const favoritesContent = document.querySelector('[value="favorites"]');
        if (favoritesContent) {
          console.log('✅ Favorites tab switching works');
        } else {
          console.log('⚠️ Favorites tab content not visible');
        }
      }, 100);
    }
    
    // Switch back to analyzer tab
    const analyzerTab = Array.from(tabs).find(tab => 
      tab.textContent.includes('Analizador')
    );
    
    if (analyzerTab) {
      analyzerTab.click();
      console.log('✅ Switched back to analyzer tab');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Tab switching test failed:', error);
    return false;
  }
}

// Test 4: Test services integration
async function testServicesIntegration() {
  console.log('\n🔧 Test 4: Testing services integration...');
  
  try {
    // Check if services are available
    if (typeof window.headlineAnalysisService === 'undefined') {
      console.log('❌ headlineAnalysisService not available');
      return false;
    }
    
    if (typeof window.supabase === 'undefined') {
      console.log('❌ supabase not available');
      return false;
    }
    
    console.log('✅ Services are available');
    
    // Test authentication
    const { data: { user }, error } = await window.supabase.auth.getUser();
    
    if (error) {
      console.log('⚠️ Authentication error:', error.message);
      return false;
    }
    
    if (user) {
      console.log('✅ User is authenticated:', user.email);
      
      // Test database connection
      try {
        const analyses = await window.headlineAnalysisService.getUserAnalyses(user.id, { limit: 1 });
        console.log('✅ Database connection works, found', analyses.length, 'analyses');
      } catch (dbError) {
        console.log('⚠️ Database connection issue:', dbError.message);
      }
    } else {
      console.log('⚠️ User not authenticated - some features will be limited');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Services integration test failed:', error);
    return false;
  }
}

// Test 5: Test analysis simulation (without actually calling API)
function testAnalysisSimulation() {
  console.log('\n🎯 Test 5: Testing analysis simulation...');
  
  try {
    // Check if we can simulate the analysis flow
    const headlineInput = document.querySelector('#headline');
    
    if (!headlineInput) {
      console.log('❌ Cannot find headline input');
      return false;
    }
    
    // Set test headline
    headlineInput.value = TEST_HEADLINE;
    headlineInput.dispatchEvent(new Event('input', { bubbles: true }));
    
    // Check if validation would pass
    const headline = headlineInput.value.trim();
    
    if (headline.length < 5) {
      console.log('❌ Headline too short for validation');
      return false;
    }
    
    if (headline.length > 500) {
      console.log('❌ Headline too long for validation');
      return false;
    }
    
    const words = headline.split(/\s+/).filter(w => w.length > 0);
    if (words.length < 2) {
      console.log('❌ Headline needs more words');
      return false;
    }
    
    if (!/[a-zA-ZáéíóúÁÉÍÓÚñÑ]/.test(headline)) {
      console.log('❌ Headline needs meaningful text');
      return false;
    }
    
    console.log('✅ Headline validation would pass');
    console.log('✅ Analysis simulation successful');
    
    return true;
  } catch (error) {
    console.error('❌ Analysis simulation test failed:', error);
    return false;
  }
}

// Main test runner
async function runIntegrationTests() {
  console.log('🚀 Running Headline Analyzer Integration Tests...\n');
  
  const results = {
    uiStructure: false,
    formFunctionality: false,
    tabSwitching: false,
    servicesIntegration: false,
    analysisSimulation: false
  };
  
  // Run tests
  results.uiStructure = testUIStructure();
  results.formFunctionality = testFormFunctionality();
  results.tabSwitching = testTabSwitching();
  results.servicesIntegration = await testServicesIntegration();
  results.analysisSimulation = testAnalysisSimulation();
  
  // Summary
  console.log('\n📊 Integration Test Results Summary:');
  console.log('==========================================');
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
  });
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All integration tests passed! The Headline Analyzer is working correctly.');
    console.log('✅ Original analysis functionality preserved');
    console.log('✅ New history/favorites system integrated');
    console.log('✅ No conflicts detected');
  } else {
    console.log('⚠️ Some integration tests failed. Please check the implementation.');
  }
  
  return results;
}

// Export functions for manual testing
window.headlineIntegrationTests = {
  runIntegrationTests,
  testUIStructure,
  testFormFunctionality,
  testTabSwitching,
  testServicesIntegration,
  testAnalysisSimulation
};

console.log('✅ Integration test script loaded. Run window.headlineIntegrationTests.runIntegrationTests() to start testing.');
